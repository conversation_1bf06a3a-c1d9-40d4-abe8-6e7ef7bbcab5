import Foundation
import SwiftData
import UserNotifications

/// 好友通知管理服务 - 星际消息中心
/// 负责管理所有好友相关的通知，包括应用内通知和推送通知
@MainActor
class EAFriendNotificationService: ObservableObject {
    
    // MARK: - 发布状态
    
    @Published var unreadCount: Int = 0
    @Published var notifications: [EAFriendNotification] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - 依赖
    
    private let repositoryContainer: EARepositoryContainer
    private let notificationCenter = UNUserNotificationCenter.current()
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
    }
    
    // MARK: - 通知创建方法
    
    /// 🔑 修复：创建好友请求通知 - 采用"传ID，勿传对象"模式确保Context安全
    func createFriendRequestNotification(
        senderProfileId: UUID,      // 传ID而非对象
        receiverProfileId: UUID,    // 传ID而非对象
        requestId: UUID,
        message: String?,
        senderUsername: String      // 传用户名用于通知显示
    ) async throws {
        // 🔑 关键修复：通过Repository创建通知，确保Context一致性
        try await repositoryContainer.friendNotificationRepository.createFriendRequestNotificationSafely(
            senderProfileId: senderProfileId,
            receiverProfileId: receiverProfileId,
            requestId: requestId,
            message: message
        )

        // 🔑 修复：通过iOS系统通知发送推送，不依赖SwiftData对象
        await sendSystemPushNotification(
            title: "新的好友请求",
            body: message?.isEmpty == false ?
                "\(senderUsername) 想要添加您为好友：\(message!)" :
                "\(senderUsername) 想要添加您为好友"
        )

        await updateUnreadCount()

        #if DEBUG
        // 调试环境下记录好友请求通知创建成功，但不使用print
        #endif
    }

    /// 🔑 修复：创建请求被接受通知 - 采用"传ID，勿传对象"模式确保Context安全
    func createRequestAcceptedNotification(
        accepterProfileId: UUID,    // 修复：传ID而非对象
        requesterProfileId: UUID,   // 修复：传ID而非对象
        friendshipId: UUID,         // 修复：传ID而非对象
        accepterUsername: String    // 传用户名用于通知显示
    ) async throws {
        // 🔑 关键修复：通过Repository安全创建通知，确保Context一致性
        try await repositoryContainer.friendNotificationRepository.createRequestAcceptedNotificationSafely(
            accepterProfileId: accepterProfileId,
            requesterProfileId: requesterProfileId,
            friendshipId: friendshipId,
            accepterUsername: accepterUsername
        )

        // 🔑 修复：通过iOS系统通知发送推送，不依赖SwiftData对象
        await sendSystemPushNotification(
            title: "好友请求已接受",
            body: "\(accepterUsername) 接受了您的好友请求，你们现在是星际伙伴了！"
        )

        await updateUnreadCount()

        #if DEBUG
        // 调试环境下记录请求接受通知创建成功，但不使用print
        #endif
    }
    
    /// 创建请求被拒绝通知
    func createRequestRejectedNotification(
        rejecterProfile: EAUserSocialProfile,
        requesterProfile: EAUserSocialProfile,
        reason: String?
    ) async throws {
        let notification = EAFriendNotification.createRequestRejectedNotification(
            rejecterProfile: rejecterProfile,
            requesterProfile: requesterProfile,
            reason: reason
        )
        
        try await saveNotification(notification)
        await updateUnreadCount()

        #if DEBUG
        // 调试环境下记录请求拒绝通知创建成功，但不使用print
        #endif
    }

    /// 创建新消息通知
    func createNewMessageNotification(
        senderProfile: EAUserSocialProfile,
        receiverProfile: EAUserSocialProfile,
        message: EAFriendMessage
    ) async throws {
        let notification = EAFriendNotification.createNewMessageNotification(
            senderProfile: senderProfile,
            receiverProfile: receiverProfile,
            message: message
        )

        try await saveNotification(notification)

        // 发送推送通知
        if notification.needsPush() {
            await sendPushNotification(notification)
        }

        await updateUnreadCount()

        #if DEBUG
        // 调试环境下记录新消息通知创建成功，但不使用print
        #endif
    }
    
    // MARK: - 通知管理方法
    
    /// 加载用户的通知列表
    func loadNotifications(for userProfile: EAUserSocialProfile) async {
        isLoading = true
        errorMessage = nil
        
        do {
            let fetchedNotifications = try await repositoryContainer.friendNotificationRepository.fetchUserNotifications(
                userProfileId: userProfile.id
            )
            
            notifications = fetchedNotifications
            await updateUnreadCount()

            #if DEBUG
            // 调试环境下记录通知加载成功，但不使用print
            #endif

        } catch {
            errorMessage = error.localizedDescription

            #if DEBUG
            // 调试环境下记录通知加载失败，但不使用print
            #endif
        }
        
        isLoading = false
    }
    
    /// 标记通知为已读
    func markAsRead(_ notificationId: UUID) async throws {
        guard let notification = notifications.first(where: { $0.id == notificationId }) else {
            throw NotificationServiceError.notificationNotFound
        }
        
        notification.markAsRead()
        try await repositoryContainer.friendNotificationRepository.updateNotification(notification)
        
        await updateUnreadCount()
        
        #if DEBUG
        // 调试环境下记录标记已读成功，但不使用print
        #endif
    }

    /// 批量标记所有通知为已读
    func markAllAsRead() async throws {
        for notification in notifications where !notification.isRead {
            notification.markAsRead()
            try await repositoryContainer.friendNotificationRepository.updateNotification(notification)
        }

        await updateUnreadCount()

        #if DEBUG
        // 调试环境下记录批量标记已读成功，但不使用print
        #endif
    }
    
    /// 删除通知
    func deleteNotification(_ notificationId: UUID) async throws {
        guard let notification = notifications.first(where: { $0.id == notificationId }) else {
            throw NotificationServiceError.notificationNotFound
        }
        
        notification.markAsDeleted()
        try await repositoryContainer.friendNotificationRepository.updateNotification(notification)
        
        // 从本地列表中移除
        notifications.removeAll { $0.id == notificationId }
        await updateUnreadCount()
        
        #if DEBUG
        // 调试环境下记录删除通知成功，但不使用print
        #endif
    }

    /// 清理过期通知
    func cleanupExpiredNotifications() async throws {
        let expiredNotifications = notifications.filter { $0.isExpired() }

        for notification in expiredNotifications {
            notification.markAsDeleted()
            try await repositoryContainer.friendNotificationRepository.updateNotification(notification)
        }

        // 从本地列表中移除过期通知
        notifications.removeAll { $0.isExpired() }
        await updateUnreadCount()

        #if DEBUG
        // 调试环境下记录清理过期通知成功，但不使用print
        #endif
    }
    
    // MARK: - 私有方法
    
    /// 保存通知到数据库
    private func saveNotification(_ notification: EAFriendNotification) async throws {
        try await repositoryContainer.friendNotificationRepository.createNotification(notification)
    }
    
    /// 更新未读数量
    private func updateUnreadCount() async {
        unreadCount = notifications.filter { !$0.isRead && !$0.isDeleted }.count
    }
    
    /// 发送推送通知
    private func sendPushNotification(_ notification: EAFriendNotification) async {
        // 检查推送权限
        let settings = await notificationCenter.notificationSettings()
        guard settings.authorizationStatus == .authorized else {
            #if DEBUG
            // 调试环境下记录推送权限未授权，但不使用print
            #endif
            return
        }
        
        // 创建推送内容
        let content = UNMutableNotificationContent()
        content.title = notification.title
        content.body = notification.content
        content.sound = .default
        content.badge = NSNumber(value: unreadCount + 1)
        
        // 设置用户信息
        content.userInfo = [
            "type": "friend_notification",
            "notificationId": notification.id.uuidString,
            "notificationType": notification.type.rawValue
        ]
        
        // 创建推送请求
        let identifier = "friend_notification_\(notification.id.uuidString)"
        let request = UNNotificationRequest(
            identifier: identifier,
            content: content,
            trigger: nil  // 立即发送
        )
        
        do {
            try await notificationCenter.add(request)
            notification.markPushSent()
            try await saveNotification(notification)

            #if DEBUG
            // 调试环境下记录推送通知发送成功，但不使用print
            #endif

        } catch {
            #if DEBUG
            // 调试环境下记录推送通知发送失败，但不使用print
            #endif
        }
    }

    /// 🔑 新增：发送系统推送通知（不依赖SwiftData对象）
    private func sendSystemPushNotification(title: String, body: String) async {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = body
        content.sound = .default

        // 创建推送请求
        let identifier = "friend_notification_\(UUID().uuidString)"
        let request = UNNotificationRequest(
            identifier: identifier,
            content: content,
            trigger: nil  // 立即发送
        )

        do {
            try await notificationCenter.add(request)

            #if DEBUG
            // 调试环境下记录推送通知发送成功，但不使用print
            #endif

        } catch {
            #if DEBUG
            // 调试环境下记录推送通知发送失败，但不使用print
            #endif
        }
    }
}

// MARK: - 错误类型

enum NotificationServiceError: Error, LocalizedError {
    case notificationNotFound
    case invalidNotificationData
    case pushNotificationFailed
    
    var errorDescription: String? {
        switch self {
        case .notificationNotFound:
            return "通知不存在"
        case .invalidNotificationData:
            return "通知数据无效"
        case .pushNotificationFailed:
            return "推送通知发送失败"
        }
    }
}
